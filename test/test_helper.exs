Code.require_file("test/support/setup.ex")

Drops.Repos.each_repo(fn repo ->
  Drops.Repos.with_owner(
    repo,
    fn _ ->
      IO.puts("Inferring and caching schemas for #{repo}")

      case Drops.Relation.SchemaCache.infer_and_cache_all_schemas(repo) do
        :ok ->
          IO.puts("✓ Schema cache populated for #{repo}")

        {:error, error} ->
          IO.puts("⚠ Failed to populate schema cache for #{repo}: #{inspect(error)}")

        :cache_disabled ->
          IO.puts("- Schema cache disabled for #{repo}")
      end
    end
  )
end)

# Code.require_file("support/test_config.ex", __DIR__)
Code.require_file("support/doctest_case.ex", __DIR__)
# Code.require_file("support/data_case.ex", __DIR__)
Code.require_file("support/contract_case.ex", __DIR__)
# Code.require_file("support/operation_case.ex", __DIR__)
Code.require_file("support/relation_case.ex", __DIR__)

# # Populate schema cache for all repos to ensure proper inference during tests
# IO.puts("Populating schema cache for test environment...")

# Drops.Repos.each_repo(fn repo ->
#   Drops.Repos.with_owner(
#     repo,
#     fn _ ->
#       IO.puts("Inferring and caching schemas for #{repo}")

#       case Drops.Relation.SchemaCache.infer_and_cache_all_schemas(repo) do
#         :ok ->
#           IO.puts("✓ Schema cache populated for #{repo}")

#         {:error, error} ->
#           IO.puts("⚠ Failed to populate schema cache for #{repo}: #{inspect(error)}")

#         :cache_disabled ->
#           IO.puts("- Schema cache disabled for #{repo}")
#       end
#     end
#   )
# end)

# ExUnit.start()
