defmodule Drops.Repos do
  def start(repo) do
    {:ok, pid} = repo.start_link()

    IO.puts("Starting repo #{repo} with pid #{inspect(pid)}")
    Ecto.Adapters.SQL.Sandbox.mode(repo, :manual)

    :persistent_term.put({:repos, repo}, pid)
  end

  def stop(repo) do
    pid = :persistent_term.get({:repos, repo, :owner})
    Ecto.Adapters.SQL.Sandbox.stop_owner(pid)
  end

  def start_owner!(repo, opts \\ []) do
    IO.puts("Starting owner for #{repo}")
    pid = Ecto.Adapters.SQL.Sandbox.start_owner!(repo, opts)

    IO.puts("Started owner for #{repo} with pid #{inspect(pid)}")

    :persistent_term.put({:repos, repo, :owner}, pid)
  end

  def stop_owner(repo) do
    Ecto.Adapters.SQL.Sandbox.stop_owner(:persistent_term.get({:repos, repo, :owner}))
  end

  def with_owner(repo, fun) do
    IO.puts("with_owner #{repo}")

    try do
      start_owner!(repo, shared: false)
      fun.(repo)
    rescue
      error ->
        IO.puts("Failed to start owner for #{repo}: #{inspect(error)}")
        reraise error, __STACKTRACE__
    after
      # Only try to stop owner if the persistent term exists
      case :persistent_term.get({:repos, repo, :owner}, :not_found) do
        :not_found -> :ok
        _pid -> stop_owner(repo)
      end
    end
  end

  def each_repo(fun) do
    Enum.each(Application.get_env(:drops, :ecto_repos), &fun.(&1))
  end
end

defmodule Drops.Repos.Sqlite do
  use Ecto.Repo,
    otp_app: :drops,
    adapter: Ecto.Adapters.SQLite3
end

defmodule Drops.Repos.Postgres do
  use Ecto.Repo,
    otp_app: :drops,
    adapter: Ecto.Adapters.Postgres
end

if Mix.env() == :test do
  # Keep the original TestRepo as a simple SQLite repo for Operations tests
  defmodule Drops.TestRepo do
    use Ecto.Repo,
      otp_app: :drops,
      adapter: Ecto.Adapters.SQLite3
  end
end

# Only start repos in test environment, and only if they're not already started
if Mix.env() == :test do
  Enum.each(Application.get_env(:drops, :ecto_repos), fn repo ->
    # Check if repo is already started to avoid conflicts
    case Process.whereis(repo) do
      nil ->
        try do
          Drops.Repos.start(repo)
        rescue
          error ->
            IO.puts("Warning: Failed to start #{repo}: #{inspect(error)}")
        end

      _pid ->
        IO.puts("#{repo} already started")
    end
  end)
end
