defmodule Drops.Repos do
  def start(repo) do
    {:ok, pid} = repo.start_link()

    IO.puts("Starting repo #{repo} with pid #{inspect(pid)}")
    Ecto.Adapters.SQL.Sandbox.mode(repo, :manual)

    :persistent_term.put({:repos, repo}, pid)
  end

  def stop(repo) do
    pid = :persistent_term.get({:repos, repo, :owner})
    Ecto.Adapters.SQL.Sandbox.stop_owner(pid)
  end

  def start_owner!(repo, opts \\ []) do
    IO.puts("Starting owner for #{repo}")
    pid = Ecto.Adapters.SQL.Sandbox.start_owner!(repo, opts)

    IO.puts("Started owner for #{repo} with pid #{inspect(pid)}")

    :persistent_term.put({:repos, repo, :owner}, pid)
  end

  def stop_owner(repo) do
    Ecto.Adapters.SQL.Sandbox.stop_owner(:persistent_term.get({:repos, repo, :owner}))
  end

  def with_owner(repo, fun) do
    IO.puts("with_owner #{repo}")

    owner_pid = nil

    try do
      owner_pid = start_owner!(repo, shared: false)
      fun.(repo)
    rescue
      error ->
        IO.puts("Failed to start owner for #{repo}: #{inspect(error)}")
        reraise error, __STACKTRACE__
    after
      if owner_pid do
        stop_owner(repo)
      end
    end
  end

  def each_repo(fun) do
    Enum.each(Application.get_env(:drops, :ecto_repos), &fun.(&1))
  end
end

defmodule Drops.Repos.Sqlite do
  use Ecto.Repo,
    otp_app: :drops,
    adapter: Ecto.Adapters.SQLite3
end

defmodule Drops.Repos.Postgres do
  use Ecto.Repo,
    otp_app: :drops,
    adapter: Ecto.Adapters.Postgres
end

if Mix.env() == :test do
  # Keep the original TestRepo as a simple SQLite repo for Operations tests
  defmodule Drops.TestRepo do
    use Ecto.Repo,
      otp_app: :drops,
      adapter: Ecto.Adapters.SQLite3
  end
end

Enum.each(Application.get_env(:drops, :ecto_repos), fn repo ->
  Drops.Repos.start(repo)
end)
